import React, {useEffect, useState} from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Star, Image, Calendar, MapPin, Clock, DollarSign, Bell, CheckCircle2, AlertCircle, MoreHorizontal, Edit, X } from 'lucide-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { useUIHelpers } from '@/hooks/use-ui-helpers';
import { CardIcon } from '@/components/ui/card-icon';
import { JobStatusBadge } from '@/components/admin/job-oversight/JobStatusBadge';
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Pagination } from '@/components/ui/pagination';
import {ApiResponse, apiService} from "@/services/api.ts";
import {useAuth} from "@/features/auth/hooks/useAuth.ts";
import { UpdateBidModal } from '@/components/provider/UpdateBidModal';
import { deleteProviderBid } from '@/services/bidService';

// region Type Definitions
interface JobAsset {
  url: string;
}

interface UserService {
  category: string;
  tasks?: string[];
}

interface UserLocation {
  address?: string;
  city?: string;
  state?: string;
}

interface UserSchedule {
  date: string;
}

interface JobUser {
  name?: string;
  avatar?: string;
}

interface ApiJob {
  id: string;
  jobId: string;
  service?: UserService;
  user?: JobUser;
  assets?: JobAsset[];
  createdAt: string;
  budget?: string | number;
  schedule?: UserSchedule;
  location?: UserLocation;
  status?: string;
  completedAt?: string;
  updatedAt?: string;
  rating?: number;
}

interface MappedLead {
  id: string;
  jobId: string;
  customer: {
    name: string;
    avatar: string;
    initials: string;
  };
  title: string;
  location: string;
  posted: string;
  budget: string | number;
  dueDate: string;
  images: string[];
  hasBid: boolean;
  bidStatus?: 'pending' | 'accepted' | 'rejected' | 'requested';
  bidId?: string;
  bidAmount?: number;
  bidDescription?: string;
  bidEstimatedTime?: string;
}

interface MappedActiveJob {
  id: string;
  customer: {
    name: string;
    avatar: string;
    initials: string;
  };
  title: string;
  location: string;
  scheduled: string;
  status: string;
  value: string | number;
}

interface MappedCompletedJob {
  id: string;
  customer: {
    name: string;
    avatar: string;
    initials: string;
  };
  title: string;
  location: string;
  completed: string;
  status: string;
  value: string | number;
  rating: number;
}

interface PaginationInfo {
  current_page: number;
  total: number;
  per_page: number;
}

interface ApiJobsResponseData {
  data: ApiJob[];
  pagination: PaginationInfo;
}
// endregion

// Helper function to get bid status badge styling
const getBidStatusBadge = (bidStatus: 'pending' | 'accepted' | 'rejected' | 'requested') => {
  switch (bidStatus) {
    case 'accepted':
      return {
        variant: 'default' as const,
        className: 'bg-green-100 text-green-800 border-green-200',
        text: 'Bid Accepted'
      };
    case 'rejected':
      return {
        variant: 'destructive' as const,
        className: 'bg-red-100 text-red-800 border-red-200',
        text: 'Bid Rejected'
      };
    case 'requested':
    case 'pending':
    default:
      return {
        variant: 'secondary' as const,
        className: 'bg-blue-100 text-blue-800 border-blue-200',
        text: 'Bid Sent'
      };
  }
};

// Components for each job state
const NewLeads = ({ onCountChange }: { onCountChange: (count: number) => void }) => {
  const navigate = useNavigate();
  const { isMobile } = useUIHelpers();
  const { logout, token } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [leads, setLeads] = useState<MappedLead[]>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalItems: 0,
    itemsPerPage: 10
  });
  const [updateBidModalOpen, setUpdateBidModalOpen] = useState(false);
  const [selectedBidForUpdate, setSelectedBidForUpdate] = useState<{
    bidId: string;
    amount: number;
    description: string;
    estimated_completion_time: string;
  } | null>(null);
  const [withdrawingBidId, setWithdrawingBidId] = useState<string | null>(null);

  const handleLeadClick = (leadId: string) => {
    navigate(`/provider/leads/${leadId}`);
  };

  const handleUpdateBid = (lead: MappedLead) => {
    if (lead.bidId && lead.bidAmount && lead.bidDescription && lead.bidEstimatedTime) {
      setSelectedBidForUpdate({
        bidId: lead.bidId,
        amount: lead.bidAmount,
        description: lead.bidDescription,
        estimated_completion_time: lead.bidEstimatedTime
      });
      setUpdateBidModalOpen(true);
    } else {
      toast({
        title: 'Error',
        description: 'Unable to update bid. Missing bid information.',
        variant: 'destructive'
      });
    }
  };

  const handleWithdrawBid = async (lead: MappedLead) => {
    if (!lead.bidId) {
      toast({
        title: 'Error',
        description: 'Unable to withdraw bid. Missing bid ID.',
        variant: 'destructive'
      });
      return;
    }

    setWithdrawingBidId(lead.bidId);

    try {
      const response = await deleteProviderBid(lead.bidId, token || undefined);

      if (response.isSuccess) {
        toast({
          title: 'Bid Withdrawn',
          description: 'Your bid has been successfully withdrawn.',
        });
        // Refresh the leads list
        fetchLeads(pagination.currentPage);
      } else {
        toast({
          title: 'Error',
          description: response.error || 'Failed to withdraw bid',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error withdrawing bid:', error);
      toast({
        title: 'Error',
        description: 'An unexpected error occurred while withdrawing the bid.',
        variant: 'destructive'
      });
    } finally {
      setWithdrawingBidId(null);
    }
  };

  const handleUpdateBidSuccess = () => {
    // Refresh the leads list after successful update
    fetchLeads(pagination.currentPage);
    setUpdateBidModalOpen(false);
    setSelectedBidForUpdate(null);
  };

  // Function to format relative time (e.g., 2 hours ago)
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds} seconds ago`;
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) {
      return `${diffInDays} ${diffInDays === 1 ? 'day' : 'days'} ago`;
    }

    const diffInMonths = Math.floor(diffInDays / 30);
    return `${diffInMonths} ${diffInMonths === 1 ? 'month' : 'months'} ago`;
  };

  // Generate a unique, subtle background color for each lead card
  const getCardColor = (index: number) => {
    const colors = [
      "bg-blue-50/80 dark:bg-blue-900/20",
      "bg-amber-50/80 dark:bg-amber-900/20",
      "bg-green-50/80 dark:bg-green-900/20",
      "bg-purple-50/80 dark:bg-purple-900/20",
      "bg-pink-50/80 dark:bg-pink-900/20"
    ];
    return colors[index % colors.length];
  };

  const getBorderColor = (index: number) => {
    const colors = [
      "border-l-blue-500",
      "border-l-amber-500",
      "border-l-green-500",
      "border-l-purple-500",
      "border-l-pink-500"
    ];
    return colors[index % colors.length];
  };


  const fetchLeads = async (page = 1) => {
    setIsLoading(true);
    setError(null);
    const authToken = token || '';

    if (!authToken) {
      setError('Authentication token is missing. Please log in again.');
      setIsLoading(false);
      toast({
        title: 'Authentication Error',
        description: 'You need to be logged in to view leads.',
        variant: 'destructive'
      });
      return;
    }

    try {
      // Fetch leads (job opportunities that providers can bid on)
      // These should be jobs with status 'open' or 'send_bids' that don't have an assigned provider yet
      const response = await apiService<ApiJobsResponseData>(`/api/job-bookings?page=${page}&status=open&job_type=send_bids`, {
        method: 'GET',
        requiresAuth: true,
        headers: {
          'Authorization': authToken
        }
      });

      if (response?.isSuccess && response?.data) {
        // Map API response to leads structure
        const mappedLeads = response.data.data.map((job: ApiJob): MappedLead => {
          // Extract tasks as a comma-separated string
          const tasks = job.service?.tasks ? job.service.tasks.join(', ') : '';
          // Combine service category and tasks for the title
          const title = job.service?.category
            ? `${job.service.category}${tasks ? ` - ${tasks}` : ''}`
            : 'Service Request';

          // Get user initials from name
          const nameParts = job.user?.name ? job.user.name.split(' ') : ['U', 'N'];
          const initials = nameParts.length > 1
            ? `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`
            : nameParts[0] ? nameParts[0].substring(0, 2) : 'UN';

          // Extract image URLs if assets exist
          const images = job.assets?.map((asset: JobAsset) => asset.url) || [];

          // Check if a bid has been sent for this job
          // In a real implementation, this would come from the API response
          // For now, we'll simulate this by checking if the job has any bids
          // Simulate some leads having bids for demonstration
          const hasBid = job.status === 'bid_sent' || job.status === 'pending_bid' ||
                        job.id.endsWith('1') || job.id.endsWith('3') || job.id.endsWith('5');

          let bidStatus: 'pending' | 'accepted' | 'rejected' | 'requested' | undefined = undefined;
          let bidId: string | undefined = undefined;
          let bidAmount: number | undefined = undefined;
          let bidDescription: string | undefined = undefined;
          let bidEstimatedTime: string | undefined = undefined;

          if (hasBid) {
            if (job.status === 'bid_accepted' || job.id.endsWith('1')) {
              bidStatus = 'accepted';
            } else if (job.status === 'bid_rejected' || job.id.endsWith('5')) {
              bidStatus = 'rejected';
            } else {
              // For demonstration, treat some as "requested" status
              bidStatus = job.id.endsWith('3') ? 'requested' : 'pending';
            }

            // Simulate bid data for demonstration
            // In a real implementation, this would come from the API response
            if (bidStatus === 'requested' || bidStatus === 'pending') {
              bidId = `bid_${job.id}`;
              bidAmount = 150 + Math.floor(Math.random() * 500); // Random amount for demo
              bidDescription = `Professional ${job.service?.category || 'service'} with quality guarantee`;
              bidEstimatedTime = '2024-01-15 10:00:00';
            }
          }

          return {
            id: job.id,
            jobId: job.jobId,
            customer: {
              name: job.user?.name || 'Customer',
              avatar: job.user?.avatar || '/placeholder.svg',
              initials: initials.toUpperCase()
            },
            title: title,
            location: job.location?.address || `${job.location?.city || 'Unknown'}, ${job.location?.state || 'Unknown'}`,
            posted: formatRelativeTime(job.createdAt),
            budget: job.budget ? (typeof job.budget === 'number' ? `$${job.budget.toLocaleString()}` : job.budget) : 'Budget not specified',
            dueDate: job.schedule?.date ? new Date(job.schedule.date).toLocaleDateString() : 'ASAP',
            images: images,
            hasBid: hasBid,
            bidStatus: bidStatus,
            bidId: bidId,
            bidAmount: bidAmount,
            bidDescription: bidDescription,
            bidEstimatedTime: bidEstimatedTime
          };
        });

        setLeads(mappedLeads);

        // Update pagination state
        if (response.data.pagination) {
          const totalItems = response.data.pagination.total || 0;
          setPagination({
            currentPage: response.data.pagination.current_page || 1,
            totalItems: totalItems,
            itemsPerPage: response.data.pagination.per_page || 10
          });
          onCountChange(totalItems); // Ensure this is called with the correct count
        } else {
          onCountChange(mappedLeads.length); // Fallback if pagination is missing
        }
      } else {
        throw new Error(response.error || 'Failed to fetch leads');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching leads';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchLeads(page);
  };

  useEffect(() => {
    fetchLeads(pagination.currentPage);
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">New Leads ({isLoading ? '...' : leads.length})</h3>
          <p className="text-sm text-muted-foreground">Job opportunities available for bidding</p>
        </div>
        <Button variant="outline" size={isMobile ? "sm" : "default"}>
          <span className="mr-1">Filter</span>
        </Button>
      </div>

      {isLoading ? (
        <div className="text-center py-10 bg-gray-50 dark:bg-gray-900/20 rounded-xl">
          <h3 className="text-lg font-medium">Loading leads...</h3>
          <p className="text-muted-foreground">Please wait while we fetch your job opportunities</p>
        </div>
      ) : error ? (
        <div className="text-center py-10 bg-red-50 dark:bg-red-900/20 rounded-xl">
          <h3 className="text-lg font-medium text-red-600">Error loading leads</h3>
          <p className="text-muted-foreground">{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => fetchLeads(pagination.currentPage)}
          >
            Try Again
          </Button>
        </div>
      ) : (
        <div className="space-y-4">
          {leads.length > 0 ? (
            leads.map((lead: MappedLead, index: number) => (
              <Card
                key={lead.id}
                className={`p-4 transition-all hover:shadow-md active:scale-98 cursor-pointer ${getCardColor(index)} border-l-4 ${getBorderColor(index)} rounded-xl overflow-hidden`}
                onClick={() => handleLeadClick(lead.jobId)}
              >
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex flex-col gap-3 flex-grow">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className="border-2 border-white shadow-sm">
                          <AvatarImage src={lead.customer.avatar} alt={lead.customer.name} />
                          <AvatarFallback className="bg-gradient-to-br from-blue-400 to-blue-600 text-white">{lead.customer.initials}</AvatarFallback>
                        </Avatar>
                        <div>
                          <span className="font-medium">{lead.customer.name}</span>
                          <div className="text-xs text-green-600 font-medium">New Lead</div>
                        </div>
                      </div>
                      {lead.hasBid ? (
                        lead.bidStatus === 'requested' || lead.bidStatus === 'pending' ? (
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleUpdateBid(lead);
                              }}
                              disabled={withdrawingBidId === lead.bidId}
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              Update
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleWithdrawBid(lead);
                              }}
                              disabled={withdrawingBidId === lead.bidId}
                            >
                              {withdrawingBidId === lead.bidId ? (
                                <AlertCircle className="h-3 w-3 mr-1 animate-spin" />
                              ) : (
                                <X className="h-3 w-3 mr-1" />
                              )}
                              Withdraw
                            </Button>
                          </div>
                        ) : (
                          (() => {
                            const badgeConfig = getBidStatusBadge(lead.bidStatus || 'pending');
                            return (
                              <Badge
                                variant={badgeConfig.variant}
                                className={badgeConfig.className}
                              >
                                {badgeConfig.text}
                              </Badge>
                            );
                          })()
                        )
                      ) : (
                        <Button
                          size="sm"
                          className="bg-green-600 hover:bg-green-700 text-white"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleLeadClick(lead.jobId);
                          }}
                        >
                          Bid Now
                        </Button>
                      )}
                    </div>

                    <div className="flex-grow space-y-1 mt-1">
                      <h4 className="font-medium text-lg">{lead.title}</h4>
                      <div className="flex flex-wrap gap-3 text-sm text-muted-foreground mt-2">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3.5 w-3.5 text-blue-500" />
                          <span>{lead.location}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3.5 w-3.5 text-amber-500" />
                          <span>Posted {lead.posted}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-3.5 w-3.5 text-green-500" />
                          <span>{lead.budget}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3.5 w-3.5 text-purple-500" />
                          <span>{lead.dueDate}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Image thumbnails */}
                  {lead.images && lead.images.length > 0 && (
                    <div className="flex-shrink-0 flex gap-2 overflow-auto py-2 mt-2 md:mt-0 w-full md:w-auto">
                      {lead.images.slice(0, 3).map((img: string, idx: number) => (
                        <div key={idx} className="relative h-16 w-16 flex-shrink-0 rounded-md overflow-hidden border border-gray-200 shadow-sm">
                          <img
                            src={img}
                            alt={`Job image ${idx + 1}`}
                            className="h-full w-full object-cover"
                          />
                          {idx === 2 && lead.images.length > 3 && (
                            <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
                              <span className="text-white text-xs font-medium">+{lead.images.length - 3}</span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                   {(!lead.images || lead.images.length === 0) && !isMobile && (
                     <div className="h-16 w-16 bg-gray-100 dark:bg-gray-800 flex-shrink-0 rounded-md flex items-center justify-center">
                       <Image className="h-6 w-6 text-gray-400 dark:text-gray-500" />
                     </div>
                   )}
                </div>
              </Card>
            ))
          ) : (
            <div className="text-center py-10 bg-gray-50 dark:bg-gray-900/20 rounded-xl">
              <h3 className="text-lg font-medium">No new leads available</h3>
              <p className="text-muted-foreground">New job opportunities will appear here when customers post jobs that match your services</p>
            </div>
          )}

          {/* Pagination */}
          {pagination.totalItems > pagination.itemsPerPage && (
            <Pagination
              totalItems={pagination.totalItems}
              itemsPerPage={pagination.itemsPerPage}
              currentPage={pagination.currentPage}
              onPageChange={handlePageChange}
            />
          )}
        </div>
      )}

      {/* Update Bid Modal */}
      <UpdateBidModal
        isOpen={updateBidModalOpen}
        onClose={() => setUpdateBidModalOpen(false)}
        bidId={selectedBidForUpdate?.bidId || ''}
        currentBid={selectedBidForUpdate ? {
          amount: selectedBidForUpdate.amount,
          description: selectedBidForUpdate.description,
          estimated_completion_time: selectedBidForUpdate.estimated_completion_time
        } : undefined}
        onSuccess={handleUpdateBidSuccess}
      />
    </div>
  );
};

const ActiveJobs = ({ onCountChange }: { onCountChange: (count: number) => void }) => {
  const { isMobile } = useUIHelpers();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { logout, token } = useAuth();
  const [selectedJob, setSelectedJob] = useState<MappedActiveJob | null>(null);
  const [updateDialogOpen, setUpdateDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeJobs, setActiveJobs] = useState<MappedActiveJob[]>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalItems: 0,
    itemsPerPage: 10
  });

  const handleJobClick = (jobId: string) => {
    navigate(`/provider/jobs/active/${jobId}`);
  };

  const handleUpdateClick = (e: React.MouseEvent, job: MappedActiveJob) => {
    e.stopPropagation(); // Prevent job card click
    setSelectedJob(job);
    setUpdateDialogOpen(true);
  };

  const handleJobUpdate = (status: string) => {
    // In a real app, this would update the job status via API
    toast({
      title: "Job Updated",
      description: `Job status updated to ${status}`,
    });
    setUpdateDialogOpen(false);
  };

  // Function to format date and time
  const formatDateTime = (dateString: string) => {
    if (!dateString) return 'Not scheduled';
    const date = new Date(dateString);
    const formattedDate = date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
    const formattedTime = date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
    return `${formattedDate} - ${formattedTime}`;
  };

  const fetchActiveJobs = async (page = 1) => {
    setIsLoading(true);
    setError(null);
    const authToken = token || '';

    if (!authToken) {
      setError('Authentication token is missing. Please log in again.');
      setIsLoading(false);
      toast({
        title: 'Authentication Error',
        description: 'You need to be logged in to view your jobs.',
        variant: 'destructive'
      });
      return;
    }

    try {
      const response = await apiService<ApiJobsResponseData>(`/api/job-bookings?page=${page}&status=active`, {
        method: 'GET',
        requiresAuth: true,
        headers: {
          'Authorization': authToken
        }
      });

      if (response.isSuccess && response.data) {
        // Map API response to the mockActiveJobs structure
        const mappedActiveJobs = response.data.data.map((job: ApiJob): MappedActiveJob => {
          // Extract tasks as a comma-separated string
          const tasks = job.service?.tasks ? job.service.tasks.join(', ') : '';
          // Combine service category and tasks for the title
          const title = job.service?.category
            ? `${job.service.category}${tasks ? ` and ${tasks}` : ''}`
            : 'Job Request';

          // Get user initials from name
          const nameParts = job.user?.name ? job.user.name.split(' ') : ['U', 'N'];
          const initials = nameParts.length > 1
            ? `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`
            : nameParts[0] ? nameParts[0].substring(0, 2) : 'UN';


          return {
            id: job.id,
            customer: {
              name: job.user?.name || 'Unknown User',
              avatar: job.user?.avatar || '/placeholder.svg',
              initials: initials.toUpperCase()
            },
            title: title,
            location: job.location?.address || 'Location not specified',
            scheduled: formatDateTime(job.schedule?.date || ''),
            status: job.status || 'Confirmed',
            value: job.budget || 'Value not specified'
          };
        });

        setActiveJobs(mappedActiveJobs);

        // Update pagination state
        if (response.data.pagination) {
          const totalItems = response.data.pagination.total || 0;
          setPagination({
            currentPage: response.data.pagination.current_page || 1,
            totalItems: totalItems,
            itemsPerPage: response.data.pagination.per_page || 10
          });
          onCountChange(totalItems);
        } else {
           onCountChange(mappedActiveJobs.length); // Fallback
        }
      } else {
        throw new Error(response.error || 'Failed to fetch active jobs');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching active jobs';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchActiveJobs(page);
  };

  useEffect(() => {
    fetchActiveJobs(pagination.currentPage);
  }, []);

  // Generate a unique, subtle background color for each card
  const getCardColor = (index: number) => {
    const colors = [
      "bg-blue-50/80 dark:bg-blue-900/20",
      "bg-green-50/80 dark:bg-green-900/20", 
      "bg-amber-50/80 dark:bg-amber-900/20",
      "bg-purple-50/80 dark:bg-purple-900/20",
      "bg-teal-50/80 dark:bg-teal-900/20"
    ];
    return colors[index % colors.length];
  };

  // Get border color based on job status
  const getBorderColor = (status: string) => {
    if (status === "Confirmed") return "border-l-green-500";
    if (status === "Awaiting Materials") return "border-l-amber-500";
    return "border-l-blue-500";
  };

  // Get background gradient based on status
  const getStatusGradient = (status: string) => {
    if (status === "Confirmed")
      return "bg-gradient-to-br from-white via-white to-green-50";
    if (status === "Awaiting Materials")
      return "bg-gradient-to-br from-white via-white to-amber-50";
    return "bg-gradient-to-br from-white via-white to-blue-50";
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Active Jobs ({isLoading ? '...' : activeJobs.length})</h3>
        <Button variant="outline" size={isMobile ? "sm" : "default"}>
          <span className="mr-1">Filter</span>
        </Button>
      </div>

      {isLoading ? (
        <div className="text-center py-10 bg-gray-50 dark:bg-gray-900/20 rounded-xl">
          <h3 className="text-lg font-medium">Loading active jobs...</h3>
          <p className="text-muted-foreground">Please wait while we fetch your active jobs</p>
        </div>
      ) : error ? (
        <div className="text-center py-10 bg-red-50 dark:bg-red-900/20 rounded-xl">
          <h3 className="text-lg font-medium text-red-600">Error loading active jobs</h3>
          <p className="text-muted-foreground">{error}</p>
          <Button 
            variant="outline" 
            className="mt-4" 
            onClick={() => fetchActiveJobs(pagination.currentPage)}
          >
            Try Again
          </Button>
        </div>
      ) : (
        <>
          <div className="hidden md:block">
            <Card className="overflow-hidden border">
              <Table>
                <TableHeader className="bg-muted/50">
                  <TableRow>
                    <TableHead className="w-[250px]">Customer</TableHead>
                    <TableHead>Job Details</TableHead>
                    <TableHead>Schedule</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {activeJobs.map((job: MappedActiveJob) => (
                    <TableRow key={job.id} className="hover:bg-muted/30 cursor-pointer" onClick={() => handleJobClick(job.id)}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-9 w-9 border">
                            <AvatarImage src={job.customer.avatar} alt={job.customer.name} />
                            <AvatarFallback className="bg-gradient-to-br from-blue-400 to-blue-600 text-white">{job.customer.initials}</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{job.customer.name}</div>
                            <div className="text-sm text-muted-foreground">{job.location}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{job.title}</div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span>{job.scheduled.split(' - ')[0]}</span>
                          <span className="text-sm text-muted-foreground">{job.scheduled.split(' - ')[1]}</span>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{job.value}</TableCell>
                      <TableCell>
                        <JobStatusBadge status={job.status} />
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          size="sm"
                          variant="outline"
                          className="bg-white hover:bg-gray-50"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedJob(job);
                            setUpdateDialogOpen(true);
                          }}
                        >
                          Update
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Card>
          </div>

          {/* Mobile view for active jobs - Enhanced version */}
          <div className="md:hidden space-y-4">
            {activeJobs.map((job: MappedActiveJob, index: number) => (
              <Card
                key={job.id}
                className={`transition-all hover:shadow-md active:scale-98 cursor-pointer
                  ${getStatusGradient(job.status)} border-l-4 ${getBorderColor(job.status)}
                  rounded-xl overflow-hidden shadow-sm`}
                onClick={() => handleJobClick(job.id)}
              >
                <div className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <Avatar className="border-2 border-white shadow-sm">
                        <AvatarImage src={job.customer.avatar} alt={job.customer.name} />
                        <AvatarFallback className={`bg-gradient-to-br ${
                          job.status === 'Confirmed'
                            ? 'from-green-400 to-emerald-600'
                            : job.status === 'Awaiting Materials'
                              ? 'from-amber-400 to-orange-600'
                              : 'from-blue-400 to-indigo-600'
                        } text-white font-medium`}>
                          {job.customer.initials}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <span className="font-medium">{job.customer.name}</span>
                        <p className="text-xs text-muted-foreground">{job.location}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <JobStatusBadge status={job.status} />
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            navigate(`/provider/jobs/active/${job.id}`);
                          }}>
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={(e) => handleUpdateClick(e, job)}>
                            Update Status
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>

                  <div className={`${
                    job.status === 'Confirmed' ? 'bg-green-50/70' : 
                    job.status === 'Awaiting Materials' ? 'bg-amber-50/70' : 
                    'bg-blue-50/70'
                  } rounded-lg p-3 mb-3 shadow-sm`}>
                    <h4 className="font-medium text-lg mb-1">{job.title}</h4>
                    <div className="flex items-center gap-1.5">
                      <DollarSign className="h-4 w-4 text-green-500" />
                      <span className="text-sm font-medium">{job.value}</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div className="bg-purple-50 rounded-lg p-2 flex items-center gap-2">
                      <CardIcon color="bg-purple-100">
                        <Calendar className="h-4 w-4 text-purple-600" />
                      </CardIcon>
                      <div className="flex flex-col">
                        <span className="text-xs text-gray-500">Date</span>
                        <span className="text-sm font-medium">{job.scheduled.split(' - ')[0]}</span>
                      </div>
                    </div>
                    <div className="bg-amber-50 rounded-lg p-2 flex items-center gap-2">
                      <CardIcon color="bg-amber-100">
                        <Clock className="h-4 w-4 text-amber-600" />
                      </CardIcon>
                      <div className="flex flex-col">
                        <span className="text-xs text-gray-500">Time</span>
                        <span className="text-sm font-medium">{job.scheduled.split(' - ')[1]}</span>
                      </div>
                    </div>
                  </div>

                  <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <Button
                      size="sm"
                      className={`w-full ${
                        job.status === 'Confirmed' 
                          ? 'bg-green-500 hover:bg-green-600' 
                          : job.status === 'Awaiting Materials'
                            ? 'bg-amber-500 hover:bg-amber-600'
                            : 'bg-blue-500 hover:bg-blue-600'
                      } text-white shadow-sm`}
                      onClick={(e) => handleUpdateClick(e, job)}
                    >
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Update Job
                    </Button>
                  </div>
                </div>
              </Card>
            ))}

            {activeJobs.length === 0 && (
              <div className="text-center py-10 bg-gray-50 dark:bg-gray-900/20 rounded-xl">
                <h3 className="text-lg font-medium">No active jobs</h3>
                <p className="text-muted-foreground">Check the leads section to find new opportunities</p>
              </div>
            )}
          </div>

          {/* Pagination */}
          {pagination.totalItems > pagination.itemsPerPage && (
            <Pagination
              totalItems={pagination.totalItems}
              itemsPerPage={pagination.itemsPerPage}
              currentPage={pagination.currentPage}
              onPageChange={handlePageChange}
            />
          )}
        </>
      )}

      {/* Job Update Dialog */}
      <Dialog open={updateDialogOpen} onOpenChange={setUpdateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Update Job Status</DialogTitle>
          </DialogHeader>

          {selectedJob && (
            <div className="py-4">
              <div className="mb-4 pb-4 border-b">
                <p className="text-sm text-muted-foreground">Job</p>
                <h4 className="font-medium">{selectedJob.title}</h4>
                <p className="text-sm">{selectedJob.customer.name}</p>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium">Select New Status</h4>
                <div className="grid grid-cols-1 gap-2">
                  <Button
                    variant="outline"
                    className={`justify-start h-auto py-3 ${selectedJob.status === 'Confirmed' ? 'border-green-500 bg-green-50' : ''}`}
                    onClick={() => handleJobUpdate('Confirmed')}
                  >
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                        <CheckCircle2 className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="text-left">
                        <p className="font-medium">Confirmed</p>
                        <p className="text-xs text-muted-foreground">Job is scheduled and confirmed</p>
                      </div>
                    </div>
                  </Button>

                  <Button
                    variant="outline"
                    className={`justify-start h-auto py-3 ${selectedJob.status === 'Awaiting Materials' ? 'border-amber-500 bg-amber-50' : ''}`}
                    onClick={() => handleJobUpdate('Awaiting Materials')}
                  >
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-amber-100 flex items-center justify-center mr-3">
                        <Clock className="h-4 w-4 text-amber-600" />
                      </div>
                      <div className="text-left">
                        <p className="font-medium">Awaiting Materials</p>
                        <p className="text-xs text-muted-foreground">Waiting for parts or supplies</p>
                      </div>
                    </div>
                  </Button>

                  <Button
                    variant="outline"
                    className="justify-start h-auto py-3"
                    onClick={() => handleJobUpdate('In Progress')}
                  >
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                        <AlertCircle className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="text-left">
                        <p className="font-medium">In Progress</p>
                        <p className="text-xs text-muted-foreground">Currently working on the job</p>
                      </div>
                    </div>
                  </Button>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setUpdateDialogOpen(false)}>Cancel</Button>
            <Button onClick={() => {
              toast({
                title: "Job Updated",
                description: "Status has been successfully updated",
              });
              setUpdateDialogOpen(false);
            }}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const CompletedJobs = ({ onCountChange }: { onCountChange: (count: number) => void }) => {
  const { isMobile } = useUIHelpers();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { logout, token } = useAuth();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [completedJobs, setCompletedJobs] = useState<MappedCompletedJob[]>([]);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalItems: 0,
    itemsPerPage: 10
  });

  const handleJobClick = (jobId: string) => {
    navigate(`/provider/jobs/completed/${jobId}`);
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not specified';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  const fetchCompletedJobs = async (page = 1) => {
    setIsLoading(true);
    setError(null);
    const authToken = token ? token.replace('Bearer ', '') : '';

    if (!authToken) {
      setError('Authentication token is missing. Please log in again.');
      setIsLoading(false);
      toast({
        title: 'Authentication Error',
        description: 'You need to be logged in to view your jobs.',
        variant: 'destructive'
      });
      return;
    }

    try {
      const response = await apiService<ApiJobsResponseData>(`/api/job-bookings?page=${page}&status=completed`, {
        method: 'GET',
        requiresAuth: true,
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (response.isSuccess && response.data) {
        // Map API response to the mockCompletedJobs structure
        const mappedCompletedJobs = response.data.data.map((job: ApiJob): MappedCompletedJob => {
          // Extract tasks as a comma-separated string
          const tasks = job.service?.tasks ? job.service.tasks.join(', ') : '';
          // Combine service category and tasks for the title
          const title = job.service?.category
            ? `${job.service.category}${tasks ? ` and ${tasks}` : ''}`
            : 'Job Request';

          // Get user initials from name
          const nameParts = job.user?.name ? job.user.name.split(' ') : ['U', 'N'];
          const initials = nameParts.length > 1
            ? `${nameParts[0][0]}${nameParts[nameParts.length - 1][0]}`
            : nameParts[0] ? nameParts[0].substring(0, 2) : 'UN';


          return {
            id: job.id,
            customer: {
              name: job.user?.name || 'Unknown User',
              avatar: job.user?.avatar || '/placeholder.svg',
              initials: initials.toUpperCase()
            },
            title: title,
            location: job.location?.address || 'Location not specified',
            completed: formatDate(job.completedAt || job.updatedAt || ''),
            status: 'Paid', // Assuming completed jobs are paid
            value: job.budget || 'Value not specified',
            rating: job.rating || 5 // Default rating if not provided
          };
        });

        setCompletedJobs(mappedCompletedJobs);

        // Update pagination state
        if (response.data.pagination) {
          const totalItems = response.data.pagination.total || 0;
          setPagination({
            currentPage: response.data.pagination.current_page || 1,
            totalItems: totalItems,
            itemsPerPage: response.data.pagination.per_page || 10
          });
          onCountChange(totalItems);
        } else {
          onCountChange(mappedCompletedJobs.length); // Fallback
        }
      } else {
        throw new Error(response.error || 'Failed to fetch completed jobs');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred while fetching completed jobs';
      setError(errorMessage);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchCompletedJobs(page);
  };

  useEffect(() => {
    fetchCompletedJobs(pagination.currentPage);
  }, []);

  // Generate a unique, subtle background color for each card
  const getCardColor = (index: number) => {
    const colors = [
      "bg-gray-50/80 dark:bg-gray-800/40",
      "bg-blue-50/60 dark:bg-blue-900/10",
      "bg-green-50/60 dark:bg-green-900/10",
      "bg-purple-50/60 dark:bg-purple-900/10"
    ];
    return colors[index % colors.length];
  };

  // Get stars display component
  const getRatingStars = (rating: number) => (
    <div className="flex">
      {Array(5).fill(0).map((_, i) => (
        <Star
          key={i}
          size={isMobile ? 14 : 16}
          className={i < rating ? "fill-amber-400 text-amber-400" : "text-gray-200"}
        />
      ))}
    </div>
  );

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Completed Jobs ({isLoading ? '...' : completedJobs.length})</h3>
        <Button variant="outline" size={isMobile ? "sm" : "default"}>
          <span className="mr-1">Filter</span>
        </Button>
      </div>

      {isLoading ? (
        <div className="text-center py-10 bg-gray-50 dark:bg-gray-900/20 rounded-xl">
          <h3 className="text-lg font-medium">Loading completed jobs...</h3>
          <p className="text-muted-foreground">Please wait while we fetch your job history</p>
        </div>
      ) : error ? (
        <div className="text-center py-10 bg-red-50 dark:bg-red-900/20 rounded-xl">
          <h3 className="text-lg font-medium text-red-600">Error loading completed jobs</h3>
          <p className="text-muted-foreground">{error}</p>
          <Button 
            variant="outline" 
            className="mt-4" 
            onClick={() => fetchCompletedJobs(pagination.currentPage)}
          >
            Try Again
          </Button>
        </div>
      ) : (
        <>
          <div className="hidden md:block">
            <Card className="overflow-hidden border">
              <Table>
                <TableHeader className="bg-muted/50">
                  <TableRow>
                    <TableHead className="w-[250px]">Customer</TableHead>
                    <TableHead>Job Details</TableHead>
                    <TableHead>Completed On</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead>Rating</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {completedJobs.map((job: MappedCompletedJob) => (
                    <TableRow key={job.id} className="hover:bg-muted/30 cursor-pointer" onClick={() => handleJobClick(job.id)}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-9 w-9 border">
                            <AvatarImage src={job.customer.avatar} alt={job.customer.name} />
                            <AvatarFallback>{job.customer.initials}</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{job.customer.name}</div>
                            <div className="text-sm text-muted-foreground">{job.location}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{job.title}</div>
                      </TableCell>
                      <TableCell>{job.completed}</TableCell>
                      <TableCell className="font-medium">{job.value}</TableCell>
                      <TableCell>
                        {getRatingStars(job.rating)}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                          {job.status}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Card>
          </div>

          {/* Mobile view for completed jobs */}
          <div className="md:hidden space-y-4">
            {completedJobs.map((job: MappedCompletedJob, index: number) => (
              <Card
                key={job.id}
                className={`transition-all hover:shadow-md active:scale-98 cursor-pointer ${getCardColor(index)} border-l-4 border-l-green-400 rounded-xl overflow-hidden`}
                onClick={() => handleJobClick(job.id)}
              >
                <div className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <Avatar className="border-2 border-white shadow-sm">
                        <AvatarImage src={job.customer.avatar} alt={job.customer.name} />
                        <AvatarFallback className="bg-gradient-to-br from-green-400 to-teal-500 text-white">{job.customer.initials}</AvatarFallback>
                      </Avatar>
                      <div>
                        <span className="font-medium">{job.customer.name}</span>
                        <p className="text-xs text-muted-foreground">{job.location}</p>
                      </div>
                    </div>
                    <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">
                      {job.status}
                    </Badge>
                  </div>

                  <div>
                    <h4 className="font-medium text-lg">{job.title}</h4>
                    <div className="grid grid-cols-1 gap-2 mt-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-1.5">
                          <Calendar className="h-4 w-4 text-purple-500" />
                          <span className="text-sm">Completed: {job.completed}</span>
                        </div>
                        <div className="flex items-center gap-1.5">
                          <DollarSign className="h-4 w-4 text-green-500" />
                          <span className="text-sm font-medium">{job.value}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">Rating:</span>
                      {getRatingStars(job.rating)}
                    </div>
                    <Button size="sm" variant="ghost" className="text-blue-600 hover:text-blue-700 hover:bg-blue-50">
                      View details
                    </Button>
                  </div>
                </div>
              </Card>
            ))}

            {completedJobs.length === 0 && (
              <div className="text-center py-10 bg-gray-50 dark:bg-gray-900/20 rounded-xl">
                <h3 className="text-lg font-medium">No completed jobs yet</h3>
                <p className="text-muted-foreground">Your job history will appear here once you complete work</p>
              </div>
            )}
          </div>

          {/* Pagination */}
          {pagination.totalItems > pagination.itemsPerPage && (
            <Pagination
              totalItems={pagination.totalItems}
              itemsPerPage={pagination.itemsPerPage}
              currentPage={pagination.currentPage}
              onPageChange={handlePageChange}
            />
          )}
        </>
      )}
    </div>
  );
};

export const JobsManagement = () => {
  const { isMobile } = useUIHelpers();
  const [leadCount, setLeadCount] = useState(0);
  const [activeJobCount, setActiveJobCount] = useState(0);
  const [completedJobCount, setCompletedJobCount] = useState(0);

  // Get tab from URL parameters
  const [searchParams, setSearchParams] = useSearchParams();
  const tabFromUrl = searchParams.get('tab') || 'leads';
  const [activeTab, setActiveTab] = useState(tabFromUrl);

  // Handle tab change and update URL
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    setSearchParams({ tab: newTab });
  };

  // Update active tab when URL changes
  useEffect(() => {
    const urlTab = searchParams.get('tab') || 'leads';
    setActiveTab(urlTab);
  }, [searchParams]);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl md:text-3xl font-bold tracking-tight mb-2">Jobs & Leads</h2>
        <p className="text-muted-foreground">Manage your job pipeline from start to finish.</p>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange}>
        <TabsList className={`grid w-full ${isMobile ? 'px-0 bg-transparent border-b h-12 rounded-none' : ''} grid-cols-3`}>
          <TabsTrigger
            value="leads"
            className={isMobile ? "data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:rounded-none shadow-none" : ""}
          >
            Leads ({leadCount})
          </TabsTrigger>
          <TabsTrigger
            value="active"
            className={isMobile ? "data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:rounded-none shadow-none" : ""}
          >
            Active ({activeJobCount})
          </TabsTrigger>
          <TabsTrigger
            value="completed"
            className={isMobile ? "data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:rounded-none shadow-none" : ""}
          >
            Completed ({completedJobCount})
          </TabsTrigger>
        </TabsList>
        <TabsContent value="leads" className="mt-6">
          <NewLeads onCountChange={setLeadCount} />
        </TabsContent>
        <TabsContent value="active" className="mt-6">
          <ActiveJobs onCountChange={setActiveJobCount} />
        </TabsContent>
        <TabsContent value="completed" className="mt-6">
          <CompletedJobs onCountChange={setCompletedJobCount} />
        </TabsContent>
      </Tabs>
    </div>
  );
};
